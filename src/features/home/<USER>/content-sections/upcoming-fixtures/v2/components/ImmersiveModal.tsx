import React, { useEffect, useState } from 'react';

interface ImmersiveModalProps {
  fixture: any;
  onClose: () => void;
  theme: any;
}

export const ImmersiveModal: React.FC<ImmersiveModalProps> = ({
  fixture,
  onClose,
  theme
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'stats' | 'lineups' | 'predictions'>('overview');

  useEffect(() => {
    setIsVisible(true);
    document.body.style.overflow = 'hidden';
    
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300);
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'stats', label: 'Statistics', icon: '📈' },
    { id: 'lineups', label: 'Lineups', icon: '👥' },
    { id: 'predictions', label: 'Predictions', icon: '🔮' }
  ];

  return (
    <div className={`
      fixed inset-0 z-50 flex items-center justify-center p-4
      transition-all duration-300 ease-out
      ${isVisible ? 'opacity-100' : 'opacity-0'}
    `}>
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={handleClose}
      />

      {/* Modal */}
      <div className={`
        relative w-full max-w-6xl max-h-[90vh] bg-white rounded-3xl shadow-2xl overflow-hidden
        transform transition-all duration-500 ease-out
        ${isVisible ? 'scale-100 translate-y-0' : 'scale-95 translate-y-8'}
      `}>
        {/* Header */}
        <div className={`relative bg-gradient-to-r ${theme.gradients.primary} text-white p-6`}>
          {/* Close Button */}
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 w-10 h-10 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
          >
            <span className="text-xl">✕</span>
          </button>

          {/* Match Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              {/* Home Team */}
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-2">
                  {fixture.homeLogo ? (
                    <img
                      src={`${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/${fixture.homeLogo}`}
                      alt={fixture.homeTeam}
                      className="w-full h-full object-contain"
                    />
                  ) : (
                    <div className="w-full h-full bg-white/20 rounded-full flex items-center justify-center text-2xl font-bold">
                      {fixture.homeTeam.charAt(0)}
                    </div>
                  )}
                </div>
                <div className="font-bold text-lg">{fixture.homeTeam}</div>
                <div className="text-sm opacity-80">{fixture.homeFlag} Home</div>
              </div>

              {/* Score/VS */}
              <div className="text-center px-8">
                {fixture.homeScore !== undefined && fixture.awayScore !== undefined ? (
                  <div className="text-4xl font-bold">
                    {fixture.homeScore} - {fixture.awayScore}
                  </div>
                ) : (
                  <div className="text-3xl font-bold opacity-60">VS</div>
                )}
                <div className="text-sm mt-2">
                  {new Date(fixture.date).toLocaleDateString()} • {new Date(fixture.date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </div>
              </div>

              {/* Away Team */}
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-2">
                  {fixture.awayLogo ? (
                    <img
                      src={`${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/${fixture.awayLogo}`}
                      alt={fixture.awayTeam}
                      className="w-full h-full object-contain"
                    />
                  ) : (
                    <div className="w-full h-full bg-white/20 rounded-full flex items-center justify-center text-2xl font-bold">
                      {fixture.awayTeam.charAt(0)}
                    </div>
                  )}
                </div>
                <div className="font-bold text-lg">{fixture.awayTeam}</div>
                <div className="text-sm opacity-80">{fixture.awayFlag} Away</div>
              </div>
            </div>

            {/* Match Info */}
            <div className="text-right">
              <div className="text-lg font-semibold">{fixture.competition}</div>
              {fixture.round && (
                <div className="text-sm opacity-80">{fixture.round}</div>
              )}
              {fixture.venue && (
                <div className="text-sm opacity-80 mt-1">🏟️ {fixture.venue}</div>
              )}
            </div>
          </div>

          {/* HOT Badge */}
          {fixture.isHot && (
            <div className="absolute top-4 left-4">
              <div className="bg-gradient-to-r from-red-500 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold flex items-center space-x-1">
                <span className="animate-pulse">🔥</span>
                <span>HOT MATCH</span>
              </div>
            </div>
          )}
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <div className="flex">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`
                  flex-1 flex items-center justify-center space-x-2 py-4 px-6
                  transition-all duration-200 ease-out
                  ${activeTab === tab.id
                    ? `border-b-2 border-blue-500 text-blue-600 bg-blue-50`
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }
                `}
              >
                <span>{tab.icon}</span>
                <span className="font-medium">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 max-h-96 overflow-y-auto">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gray-50 rounded-xl p-4">
                  <div className="text-sm text-gray-600 mb-1">Status</div>
                  <div className="font-semibold text-lg">{fixture.status}</div>
                </div>
                <div className="bg-gray-50 rounded-xl p-4">
                  <div className="text-sm text-gray-600 mb-1">Importance</div>
                  <div className="font-semibold text-lg">{fixture.importance || 'Medium'}/10</div>
                </div>
                <div className="bg-gray-50 rounded-xl p-4">
                  <div className="text-sm text-gray-600 mb-1">Social Buzz</div>
                  <div className="font-semibold text-lg">{fixture.socialBuzz || '0'} mentions</div>
                </div>
              </div>

              {fixture.aiInsights && (
                <div className="bg-blue-50 rounded-xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-blue-600">🤖</span>
                    <div className="font-semibold text-blue-900">AI Insights</div>
                  </div>
                  <div className="text-blue-800">{fixture.aiInsights}</div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'stats' && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📊</div>
              <div className="text-xl font-semibold text-gray-900 mb-2">Statistics Coming Soon</div>
              <div className="text-gray-600">Detailed match statistics will be available here</div>
            </div>
          )}

          {activeTab === 'lineups' && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">👥</div>
              <div className="text-xl font-semibold text-gray-900 mb-2">Lineups Coming Soon</div>
              <div className="text-gray-600">Team lineups and formations will be displayed here</div>
            </div>
          )}

          {activeTab === 'predictions' && (
            <div className="space-y-6">
              {fixture.predictedScore && (
                <div className="bg-purple-50 rounded-xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-purple-600">🔮</span>
                    <div className="font-semibold text-purple-900">AI Prediction</div>
                  </div>
                  <div className="text-2xl font-bold text-purple-800">
                    {fixture.predictedScore.home} - {fixture.predictedScore.away}
                  </div>
                  <div className="text-sm text-purple-600 mt-1">
                    Confidence: {Math.round((fixture.predictedScore.confidence || 0) * 100)}%
                  </div>
                </div>
              )}
              
              <div className="text-center py-8">
                <div className="text-4xl mb-4">🎯</div>
                <div className="text-lg font-semibold text-gray-900 mb-2">More Predictions Coming</div>
                <div className="text-gray-600">Advanced match predictions and betting insights</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
