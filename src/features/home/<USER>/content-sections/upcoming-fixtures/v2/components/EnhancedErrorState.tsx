import React, { useState } from 'react';

interface EnhancedErrorStateProps {
  error: string;
  onRetry: () => void;
  theme: any;
}

export const EnhancedErrorState: React.FC<EnhancedErrorStateProps> = ({
  error,
  onRetry,
  theme
}) => {
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = async () => {
    setIsRetrying(true);
    try {
      await onRetry();
    } finally {
      setTimeout(() => setIsRetrying(false), 1000);
    }
  };

  const getErrorType = (errorMessage: string) => {
    if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
      return {
        type: 'network',
        icon: '🌐',
        title: 'Connection Issue',
        description: 'Unable to connect to the server. Please check your internet connection.',
        suggestions: [
          'Check your internet connection',
          'Try refreshing the page',
          'Contact support if the issue persists'
        ]
      };
    }
    
    if (errorMessage.includes('404') || errorMessage.includes('not found')) {
      return {
        type: 'notfound',
        icon: '🔍',
        title: 'Data Not Found',
        description: 'The requested fixtures data could not be found.',
        suggestions: [
          'Try adjusting your filters',
          'Check back later for updates',
          'Browse other available fixtures'
        ]
      };
    }
    
    if (errorMessage.includes('500') || errorMessage.includes('server')) {
      return {
        type: 'server',
        icon: '🔧',
        title: 'Server Error',
        description: 'Our servers are experiencing issues. We\'re working to fix this.',
        suggestions: [
          'Try again in a few minutes',
          'Clear your browser cache',
          'Contact support if needed'
        ]
      };
    }
    
    return {
      type: 'general',
      icon: '⚠️',
      title: 'Something Went Wrong',
      description: 'An unexpected error occurred while loading fixtures.',
      suggestions: [
        'Try refreshing the page',
        'Clear your browser cache',
        'Contact support if the issue continues'
      ]
    };
  };

  const errorInfo = getErrorType(error);

  return (
    <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
      {/* Error Icon with Animation */}
      <div className="relative mb-8">
        <div className={`
          w-24 h-24 rounded-full bg-gradient-to-br from-red-100 to-red-200 
          flex items-center justify-center text-4xl animate-bounce-subtle
        `}>
          {errorInfo.icon}
        </div>
        
        {/* Error Pulse Ring */}
        <div className="absolute inset-0 rounded-full border-4 border-red-300 animate-ping opacity-20"></div>
      </div>

      {/* Error Content */}
      <div className="max-w-md">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          {errorInfo.title}
        </h3>
        
        <p className="text-gray-600 mb-6">
          {errorInfo.description}
        </p>

        {/* Error Details (Collapsible) */}
        <details className="mb-6 text-left">
          <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700 mb-2">
            Technical Details
          </summary>
          <div className="bg-gray-50 rounded-lg p-3 text-xs text-gray-700 font-mono">
            {error}
          </div>
        </details>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <button
            onClick={handleRetry}
            disabled={isRetrying}
            className={`
              flex items-center justify-center space-x-2 px-6 py-3 rounded-xl
              bg-gradient-to-r ${theme.gradients.primary} text-white font-medium
              hover:shadow-lg transform transition-all duration-200
              hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed
            `}
          >
            {isRetrying ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Retrying...</span>
              </>
            ) : (
              <>
                <span>🔄</span>
                <span>Try Again</span>
              </>
            )}
          </button>

          <button
            onClick={() => window.location.reload()}
            className="
              flex items-center justify-center space-x-2 px-6 py-3 rounded-xl
              bg-gray-100 text-gray-700 font-medium hover:bg-gray-200
              transform transition-all duration-200 hover:scale-105 active:scale-95
            "
          >
            <span>↻</span>
            <span>Refresh Page</span>
          </button>
        </div>

        {/* Suggestions */}
        <div className="mt-8 text-left">
          <h4 className="font-semibold text-gray-900 mb-3">What you can try:</h4>
          <ul className="space-y-2">
            {errorInfo.suggestions.map((suggestion, index) => (
              <li key={index} className="flex items-start space-x-2 text-sm text-gray-600">
                <span className="text-blue-500 mt-0.5">•</span>
                <span>{suggestion}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Contact Support */}
        <div className="mt-8 p-4 bg-blue-50 rounded-xl">
          <div className="flex items-center justify-center space-x-2 text-blue-700">
            <span>💬</span>
            <span className="font-medium">Need help?</span>
          </div>
          <p className="text-sm text-blue-600 mt-1">
            Contact our support team if the issue persists
          </p>
        </div>
      </div>

      {/* Background Decoration */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-5">
        <div className="absolute -top-40 -right-40 w-80 h-80 rounded-full bg-red-500 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 rounded-full bg-orange-500 blur-3xl"></div>
      </div>
    </div>
  );
};
