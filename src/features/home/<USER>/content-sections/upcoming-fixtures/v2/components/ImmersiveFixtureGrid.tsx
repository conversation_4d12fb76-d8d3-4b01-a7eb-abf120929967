import React, { useState, useRef, useEffect } from 'react';
import { AdvancedFixtureCard } from './AdvancedFixtureCard';
import { TimelineView } from './TimelineView';
import { ImmersiveModal } from './ImmersiveModal';

interface ImmersiveFixtureGridProps {
  fixtures: any[];
  leagues: any[];
  viewMode: 'grid' | 'timeline' | 'immersive';
  selectedLeague: string | null;
  searchQuery: string;
  isLoading: boolean;
  error: string | null;
  theme: any;
  onFixtureSelect: (fixture: any) => void;
}

export const ImmersiveFixtureGrid: React.FC<ImmersiveFixtureGridProps> = ({
  fixtures,
  leagues,
  viewMode,
  selectedLeague,
  searchQuery,
  isLoading,
  error,
  theme,
  onFixtureSelect
}) => {
  const [selectedFixture, setSelectedFixture] = useState<any>(null);
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const [visibleCards, setVisibleCards] = useState<Set<string>>(new Set());
  const gridRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Intersection Observer for scroll animations
  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setVisibleCards(prev => new Set([...prev, entry.target.id]));
          }
        });
      },
      { threshold: 0.1, rootMargin: '50px' }
    );

    return () => observerRef.current?.disconnect();
  }, []);

  // Filter fixtures based on search and league
  const filteredFixtures = fixtures.filter(fixture => {
    const matchesSearch = !searchQuery || 
      fixture.homeTeam.toLowerCase().includes(searchQuery.toLowerCase()) ||
      fixture.awayTeam.toLowerCase().includes(searchQuery.toLowerCase()) ||
      fixture.competition.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesLeague = !selectedLeague || fixture.leagueId === selectedLeague;
    
    return matchesSearch && matchesLeague;
  });

  // Group fixtures by time periods for better organization
  const groupedFixtures = React.useMemo(() => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

    const groups = {
      live: [] as any[],
      today: [] as any[],
      tomorrow: [] as any[],
      thisWeek: [] as any[],
      later: [] as any[]
    };

    filteredFixtures.forEach(fixture => {
      const fixtureDate = new Date(fixture.date);
      
      if (['1H', '2H', 'HT', 'LIVE'].includes(fixture.status)) {
        groups.live.push(fixture);
      } else if (fixtureDate >= today && fixtureDate < tomorrow) {
        groups.today.push(fixture);
      } else if (fixtureDate >= tomorrow && fixtureDate < new Date(tomorrow.getTime() + 24 * 60 * 60 * 1000)) {
        groups.tomorrow.push(fixture);
      } else if (fixtureDate < nextWeek) {
        groups.thisWeek.push(fixture);
      } else {
        groups.later.push(fixture);
      }
    });

    return groups;
  }, [filteredFixtures]);

  const handleCardClick = (fixture: any) => {
    setSelectedFixture(fixture);
    onFixtureSelect(fixture);
  };

  if (viewMode === 'timeline') {
    return (
      <TimelineView
        groupedFixtures={groupedFixtures}
        onFixtureSelect={handleCardClick}
        theme={theme}
      />
    );
  }

  if (viewMode === 'immersive') {
    return (
      <div className="min-h-screen bg-black text-white p-8">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl font-bold mb-8 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            Immersive Experience
          </h2>
          {/* Immersive view implementation */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {filteredFixtures.slice(0, 6).map((fixture, index) => (
              <div
                key={fixture.id}
                className="relative group cursor-pointer"
                onClick={() => handleCardClick(fixture)}
              >
                <div className="aspect-video bg-gradient-to-br from-gray-900 to-black rounded-2xl overflow-hidden transform transition-all duration-500 hover:scale-105 hover:rotate-1">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent z-10"></div>
                  <div className="absolute bottom-0 left-0 right-0 p-6 z-20">
                    <div className="text-2xl font-bold mb-2">
                      {fixture.homeTeam} vs {fixture.awayTeam}
                    </div>
                    <div className="text-blue-400">{fixture.competition}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Grid View (Default)
  return (
    <div className="container mx-auto px-4 py-8" ref={gridRef}>
      {/* Live Matches Section */}
      {groupedFixtures.live.length > 0 && (
        <section className="mb-12">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
            <h2 className="text-2xl font-bold text-gray-900">Live Now</h2>
            <div className="flex-1 h-px bg-gradient-to-r from-red-500 to-transparent"></div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {groupedFixtures.live.map((fixture, index) => (
              <AdvancedFixtureCard
                key={fixture.id}
                fixture={fixture}
                index={index}
                isVisible={visibleCards.has(fixture.id)}
                isHovered={hoveredCard === fixture.id}
                onHover={setHoveredCard}
                onClick={() => handleCardClick(fixture)}
                theme={theme}
                variant="live"
              />
            ))}
          </div>
        </section>
      )}

      {/* Today's Matches */}
      {groupedFixtures.today.length > 0 && (
        <section className="mb-12">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
            <h2 className="text-2xl font-bold text-gray-900">Today</h2>
            <div className="flex-1 h-px bg-gradient-to-r from-blue-500 to-transparent"></div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {groupedFixtures.today.map((fixture, index) => (
              <AdvancedFixtureCard
                key={fixture.id}
                fixture={fixture}
                index={index}
                isVisible={visibleCards.has(fixture.id)}
                isHovered={hoveredCard === fixture.id}
                onHover={setHoveredCard}
                onClick={() => handleCardClick(fixture)}
                theme={theme}
                variant="today"
              />
            ))}
          </div>
        </section>
      )}

      {/* Tomorrow's Matches */}
      {groupedFixtures.tomorrow.length > 0 && (
        <section className="mb-12">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-4 h-4 bg-green-500 rounded-full"></div>
            <h2 className="text-2xl font-bold text-gray-900">Tomorrow</h2>
            <div className="flex-1 h-px bg-gradient-to-r from-green-500 to-transparent"></div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {groupedFixtures.tomorrow.map((fixture, index) => (
              <AdvancedFixtureCard
                key={fixture.id}
                fixture={fixture}
                index={index}
                isVisible={visibleCards.has(fixture.id)}
                isHovered={hoveredCard === fixture.id}
                onHover={setHoveredCard}
                onClick={() => handleCardClick(fixture)}
                theme={theme}
                variant="upcoming"
              />
            ))}
          </div>
        </section>
      )}

      {/* This Week */}
      {groupedFixtures.thisWeek.length > 0 && (
        <section className="mb-12">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-4 h-4 bg-purple-500 rounded-full"></div>
            <h2 className="text-2xl font-bold text-gray-900">This Week</h2>
            <div className="flex-1 h-px bg-gradient-to-r from-purple-500 to-transparent"></div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
            {groupedFixtures.thisWeek.map((fixture, index) => (
              <AdvancedFixtureCard
                key={fixture.id}
                fixture={fixture}
                index={index}
                isVisible={visibleCards.has(fixture.id)}
                isHovered={hoveredCard === fixture.id}
                onHover={setHoveredCard}
                onClick={() => handleCardClick(fixture)}
                theme={theme}
                variant="compact"
              />
            ))}
          </div>
        </section>
      )}

      {/* Immersive Modal */}
      {selectedFixture && (
        <ImmersiveModal
          fixture={selectedFixture}
          onClose={() => setSelectedFixture(null)}
          theme={theme}
        />
      )}

      {/* Empty State */}
      {filteredFixtures.length === 0 && !isLoading && (
        <div className="text-center py-16">
          <div className="text-6xl mb-4">⚽</div>
          <h3 className="text-2xl font-bold text-gray-900 mb-2">No matches found</h3>
          <p className="text-gray-600">Try adjusting your search or filters</p>
        </div>
      )}
    </div>
  );
};
