import React, { useState, useEffect, useRef } from 'react';

interface AdvancedFixtureCardProps {
  fixture: any;
  index: number;
  isVisible: boolean;
  isHovered: boolean;
  onHover: (id: string | null) => void;
  onClick: () => void;
  theme: any;
  variant: 'live' | 'today' | 'upcoming' | 'compact';
}

export const AdvancedFixtureCard: React.FC<AdvancedFixtureCardProps> = ({
  fixture,
  index,
  isVisible,
  isHovered,
  onHover,
  onClick,
  theme,
  variant
}) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isLoaded, setIsLoaded] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => setIsLoaded(true), index * 100);
      return () => clearTimeout(timer);
    }
  }, [isVisible, index]);

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!cardRef.current) return;
    
    const rect = cardRef.current.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width - 0.5) * 20;
    const y = ((e.clientY - rect.top) / rect.height - 0.5) * 20;
    
    setMousePosition({ x, y });
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'live':
        return {
          container: 'aspect-[4/3] bg-gradient-to-br from-red-500/10 to-orange-500/10 border-red-200',
          glow: 'shadow-red-500/20',
          accent: 'from-red-500 to-orange-500'
        };
      case 'today':
        return {
          container: 'aspect-[4/3] bg-gradient-to-br from-blue-500/10 to-purple-500/10 border-blue-200',
          glow: 'shadow-blue-500/20',
          accent: 'from-blue-500 to-purple-500'
        };
      case 'upcoming':
        return {
          container: 'aspect-[4/3] bg-gradient-to-br from-green-500/10 to-emerald-500/10 border-green-200',
          glow: 'shadow-green-500/20',
          accent: 'from-green-500 to-emerald-500'
        };
      case 'compact':
        return {
          container: 'aspect-[3/2] bg-gradient-to-br from-gray-500/10 to-slate-500/10 border-gray-200',
          glow: 'shadow-gray-500/20',
          accent: 'from-gray-500 to-slate-500'
        };
      default:
        return {
          container: 'aspect-[4/3] bg-white border-gray-200',
          glow: 'shadow-gray-500/20',
          accent: 'from-blue-500 to-purple-500'
        };
    }
  };

  const styles = getVariantStyles();

  const cardClasses = `
    group relative cursor-pointer overflow-hidden rounded-2xl border-2 transition-all duration-500 ease-out
    ${styles.container}
    ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}
    ${isHovered ? `shadow-2xl ${styles.glow} scale-105 -rotate-1` : 'shadow-lg hover:shadow-xl'}
    ${fixture.isHot ? 'ring-2 ring-orange-400/50 animate-pulse-subtle' : ''}
  `;

  const transformStyle = isHovered ? {
    transform: `perspective(1000px) rotateX(${mousePosition.y * 0.1}deg) rotateY(${mousePosition.x * 0.1}deg) translateZ(20px)`
  } : {};

  return (
    <div
      ref={cardRef}
      className={cardClasses}
      style={transformStyle}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => onHover(fixture.id)}
      onMouseLeave={() => {
        onHover(null);
        setMousePosition({ x: 0, y: 0 });
      }}
      onClick={onClick}
    >
      {/* 3D Background Effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      
      {/* HOT Effect Overlay */}
      {fixture.isHot && (
        <>
          <div className="absolute inset-0 bg-gradient-to-r from-orange-400/20 via-red-400/20 to-orange-400/20 animate-gradient-x"></div>
          <div className="absolute top-2 right-2 z-20">
            <div className="bg-gradient-to-r from-red-500 to-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center space-x-1 animate-bounce-subtle">
              <span className="animate-pulse">🔥</span>
              <span>HOT</span>
            </div>
          </div>
        </>
      )}

      {/* Live Indicator */}
      {variant === 'live' && (
        <div className="absolute top-2 left-2 z-20">
          <div className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center space-x-1">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <span>LIVE</span>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="relative z-10 h-full p-4 flex flex-col">
        {/* Competition Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="text-xs font-medium text-gray-600 bg-white/80 px-2 py-1 rounded-full">
            {fixture.competition}
          </div>
          {fixture.round && (
            <div className="text-xs text-gray-500 bg-gray-100/80 px-2 py-1 rounded">
              {fixture.round}
            </div>
          )}
        </div>

        {/* Teams Section */}
        <div className="flex-1 flex flex-col justify-center">
          {/* Home Team */}
          <div className="flex items-center space-x-3 mb-2">
            <div className="relative w-8 h-8 flex-shrink-0">
              {fixture.homeLogo ? (
                <img
                  src={`${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/${fixture.homeLogo}`}
                  alt={fixture.homeTeam}
                  className="w-full h-full object-contain rounded-full shadow-sm"
                  onError={(e: any) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {fixture.homeTeam.charAt(0)}
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="font-semibold text-sm text-gray-900 truncate group-hover:text-blue-600 transition-colors">
                {fixture.homeTeam}
              </div>
              <div className="text-xs text-gray-500">Home</div>
            </div>
            {fixture.homeScore !== undefined && (
              <div className="text-lg font-bold text-gray-900">
                {fixture.homeScore}
              </div>
            )}
          </div>

          {/* VS or Score */}
          <div className="text-center py-1">
            {fixture.homeScore !== undefined && fixture.awayScore !== undefined ? (
              <div className="text-xs text-gray-500">vs</div>
            ) : (
              <div className="text-sm font-semibold text-gray-400">VS</div>
            )}
          </div>

          {/* Away Team */}
          <div className="flex items-center space-x-3 mt-2">
            <div className="relative w-8 h-8 flex-shrink-0">
              {fixture.awayLogo ? (
                <img
                  src={`${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/${fixture.awayLogo}`}
                  alt={fixture.awayTeam}
                  className="w-full h-full object-contain rounded-full shadow-sm"
                  onError={(e: any) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-red-500 to-orange-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {fixture.awayTeam.charAt(0)}
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="font-semibold text-sm text-gray-900 truncate group-hover:text-blue-600 transition-colors">
                {fixture.awayTeam}
              </div>
              <div className="text-xs text-gray-500">Away</div>
            </div>
            {fixture.awayScore !== undefined && (
              <div className="text-lg font-bold text-gray-900">
                {fixture.awayScore}
              </div>
            )}
          </div>
        </div>

        {/* Footer Info */}
        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center space-x-1">
              <span>⏰</span>
              <span>{new Date(fixture.date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
            </div>
            {fixture.venue && (
              <div className="flex items-center space-x-1 truncate">
                <span>🏟️</span>
                <span className="truncate">{fixture.venue}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Hover Glow Effect */}
      <div className={`
        absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300
        bg-gradient-to-r ${styles.accent} blur-xl -z-10 scale-110
      `}></div>

      {/* Click Ripple Effect */}
      <div className="absolute inset-0 rounded-2xl overflow-hidden">
        <div className="absolute inset-0 bg-white/20 scale-0 group-active:scale-100 transition-transform duration-200 rounded-2xl"></div>
      </div>
    </div>
  );
};
