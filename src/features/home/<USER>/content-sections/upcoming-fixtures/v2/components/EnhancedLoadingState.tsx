import React from 'react';

interface EnhancedLoadingStateProps {
  theme: any;
  message?: string;
  progress?: number;
}

export const EnhancedLoadingState: React.FC<EnhancedLoadingStateProps> = ({
  theme,
  message = 'Loading amazing fixtures...',
  progress = 0
}) => {
  return (
    <div className="flex flex-col items-center justify-center py-16 px-4">
      {/* Main Loading Animation */}
      <div className="relative mb-8">
        {/* Outer Ring */}
        <div className={`
          w-20 h-20 rounded-full border-4 border-transparent 
          ${theme.loaderBorder} animate-spin
        `}></div>
        
        {/* Inner Core */}
        <div className={`
          absolute inset-2 rounded-full ${theme.loaderCore} 
          animate-pulse flex items-center justify-center
        `}>
          <span className="text-white text-xl">⚽</span>
        </div>

        {/* Floating Particles */}
        <div className="absolute inset-0">
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className={`
                absolute w-2 h-2 rounded-full ${theme.particles} opacity-60
                animate-float-random
              `}
              style={{
                left: `${20 + Math.cos(i * 60 * Math.PI / 180) * 30}px`,
                top: `${20 + Math.sin(i * 60 * Math.PI / 180) * 30}px`,
                animationDelay: `${i * 0.2}s`,
                animationDuration: `${2 + Math.random()}s`
              }}
            />
          ))}
        </div>
      </div>

      {/* Loading Message */}
      <div className="text-center max-w-md">
        <h3 className={`text-xl font-semibold mb-2 ${theme.titleText}`}>
          {message}
        </h3>
        <p className={`text-sm ${theme.subtitleText} mb-4`}>
          Fetching the latest match data with AI enhancements
        </p>

        {/* Progress Bar */}
        {progress > 0 && (
          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
            <div 
              className={`h-2 rounded-full bg-gradient-to-r ${theme.gradients.primary} transition-all duration-300`}
              style={{ width: `${Math.min(progress, 100)}%` }}
            ></div>
          </div>
        )}

        {/* Loading Steps */}
        <div className="flex justify-center space-x-4 text-xs">
          {[
            { label: 'Fixtures', icon: '⚽' },
            { label: 'Leagues', icon: '🏆' },
            { label: 'AI Analysis', icon: '🤖' }
          ].map((step, index) => (
            <div
              key={step.label}
              className={`
                flex items-center space-x-1 px-2 py-1 rounded-full
                ${index <= Math.floor(progress / 33) 
                  ? `bg-gradient-to-r ${theme.gradients.primary} text-white` 
                  : 'bg-gray-100 text-gray-500'
                }
                transition-all duration-500
              `}
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <span>{step.icon}</span>
              <span className="font-medium">{step.label}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Skeleton Cards Preview */}
      <div className="mt-12 w-full max-w-4xl">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="bg-white rounded-xl border border-gray-200 p-4 animate-pulse"
              style={{ animationDelay: `${i * 0.1}s` }}
            >
              {/* Competition */}
              <div className="h-4 bg-gray-200 rounded w-1/3 mb-3"></div>
              
              {/* Teams */}
              <div className="space-y-3">
                {/* Home Team */}
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                  <div className="h-4 bg-gray-200 rounded flex-1"></div>
                  <div className="w-6 h-6 bg-gray-200 rounded"></div>
                </div>
                
                {/* VS */}
                <div className="text-center">
                  <div className="h-4 bg-gray-200 rounded w-8 mx-auto"></div>
                </div>
                
                {/* Away Team */}
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                  <div className="h-4 bg-gray-200 rounded flex-1"></div>
                  <div className="w-6 h-6 bg-gray-200 rounded"></div>
                </div>
              </div>
              
              {/* Footer */}
              <div className="mt-4 pt-3 border-t border-gray-100">
                <div className="flex justify-between">
                  <div className="h-3 bg-gray-200 rounded w-16"></div>
                  <div className="h-3 bg-gray-200 rounded w-12"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
