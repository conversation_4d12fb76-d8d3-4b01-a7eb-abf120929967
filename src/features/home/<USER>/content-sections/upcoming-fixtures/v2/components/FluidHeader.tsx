import React, { useState, useEffect } from 'react';

interface FluidHeaderProps {
  viewMode: 'grid' | 'timeline' | 'immersive';
  onViewModeChange: (mode: 'grid' | 'timeline' | 'immersive') => void;
  isFullscreen: boolean;
  onToggleFullscreen: () => void;
  theme: any;
}

export const FluidHeader: React.FC<FluidHeaderProps> = ({
  viewMode,
  onViewModeChange,
  isFullscreen,
  onToggleFullscreen,
  theme
}) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearInterval(timeInterval);
    };
  }, []);

  const headerClasses = `
    sticky top-0 z-40 transition-all duration-500 ease-out
    ${isScrolled 
      ? 'bg-white/80 backdrop-blur-xl shadow-2xl border-b border-white/20' 
      : 'bg-transparent'
    }
    ${isFullscreen ? 'hidden' : ''}
  `;

  const titleClasses = `
    font-bold transition-all duration-500 ease-out
    ${isScrolled ? 'text-2xl' : 'text-4xl md:text-5xl'}
    ${theme.titleText}
  `;

  const subtitleClasses = `
    transition-all duration-500 ease-out
    ${isScrolled ? 'text-sm opacity-70' : 'text-lg md:text-xl opacity-90'}
    ${theme.subtitleText}
  `;

  return (
    <header className={headerClasses}>
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          {/* Morphing Title Section */}
          <div className="flex-1">
            <div className="relative overflow-hidden">
              <h1 className={titleClasses}>
                <span className="inline-block animate-slide-in-left">Upcoming</span>
                <span className="inline-block ml-3 animate-slide-in-right bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Fixtures
                </span>
              </h1>
              
              {!isScrolled && (
                <p className={subtitleClasses}>
                  Discover the most exciting matches with immersive experience
                </p>
              )}
            </div>

            {/* Live Time Display */}
            <div className={`mt-2 flex items-center space-x-2 ${theme.timeText}`}>
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium">
                {currentTime.toLocaleTimeString()} • Live Updates
              </span>
            </div>
          </div>

          {/* View Mode Switcher with 3D Effects */}
          <div className="flex items-center space-x-4">
            {/* View Mode Buttons */}
            <div className="flex bg-white/10 backdrop-blur-sm rounded-2xl p-1 border border-white/20">
              {[
                { mode: 'grid' as const, icon: '⊞', label: 'Grid' },
                { mode: 'timeline' as const, icon: '⧗', label: 'Timeline' },
                { mode: 'immersive' as const, icon: '◉', label: 'Immersive' }
              ].map(({ mode, icon, label }) => (
                <button
                  key={mode}
                  onClick={() => onViewModeChange(mode)}
                  className={`
                    relative px-4 py-2 rounded-xl transition-all duration-300 ease-out
                    transform hover:scale-105 active:scale-95
                    ${viewMode === mode
                      ? `${theme.activeButton} shadow-lg scale-105`
                      : `${theme.inactiveButton} hover:bg-white/10`
                    }
                  `}
                  title={label}
                >
                  <span className="text-lg">{icon}</span>
                  {viewMode === mode && (
                    <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/20 to-purple-500/20 animate-pulse"></div>
                  )}
                </button>
              ))}
            </div>

            {/* Fullscreen Toggle */}
            <button
              onClick={onToggleFullscreen}
              className={`
                p-3 rounded-xl transition-all duration-300 ease-out
                transform hover:scale-110 active:scale-95
                ${theme.fullscreenButton}
                hover:shadow-lg
              `}
              title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
            >
              <span className="text-xl">
                {isFullscreen ? '⤓' : '⤢'}
              </span>
            </button>
          </div>
        </div>

        {/* Progress Bar for Scroll */}
        {isScrolled && (
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-purple-500 transform origin-left animate-scale-x"></div>
        )}

        {/* Floating Stats (when not scrolled) */}
        {!isScrolled && (
          <div className="mt-8 grid grid-cols-3 gap-4 max-w-md">
            {[
              { label: 'Live Matches', value: '12', icon: '⚡' },
              { label: 'Today', value: '24', icon: '📅' },
              { label: 'This Week', value: '156', icon: '📊' }
            ].map((stat, index) => (
              <div
                key={stat.label}
                className={`
                  bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20
                  transform transition-all duration-500 ease-out hover:scale-105
                  animate-fade-in-up
                `}
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="text-center">
                  <div className="text-2xl mb-1">{stat.icon}</div>
                  <div className={`text-2xl font-bold ${theme.statValue}`}>
                    {stat.value}
                  </div>
                  <div className={`text-xs ${theme.statLabel}`}>
                    {stat.label}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </header>
  );
};
