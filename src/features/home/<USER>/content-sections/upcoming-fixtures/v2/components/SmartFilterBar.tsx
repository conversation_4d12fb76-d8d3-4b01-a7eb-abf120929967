import React, { useState, useRef, useEffect, useMemo } from 'react';

interface SmartFilterBarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  selectedLeague: string | null;
  onLeagueChange: (league: string | null) => void;
  filters: any;
  onFiltersChange: (filters: any) => void;
  suggestions: string[];
  recommendations: any[];
  theme: any;
}

export const SmartFilterBar: React.FC<SmartFilterBarProps> = ({
  searchQuery,
  onSearchChange,
  selectedLeague,
  onLeagueChange,
  filters,
  onFiltersChange,
  suggestions,
  recommendations,
  theme
}) => {
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [activeFilter, setActiveFilter] = useState<string | null>(null);
  const searchRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
        setIsSearchFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const filterOptions = [
    { key: 'today', label: 'Today', icon: '📅', count: 24 },
    { key: 'live', label: 'Live Now', icon: '🔴', count: 8 },
    { key: 'hot', label: 'Hot Matches', icon: '🔥', count: 12 },
    { key: 'favorites', label: 'Favorites', icon: '⭐', count: 6 },
    { key: 'trending', label: 'Trending', icon: '📈', count: 15 }
  ];

  // Get quick leagues from props or use defaults
  const quickLeagues = useMemo(() => {
    // If we have real leagues data, use top priority leagues
    if (Array.isArray((window as any).v2Leagues) && (window as any).v2Leagues.length > 0) {
      return (window as any).v2Leagues
        .sort((a: any, b: any) => (b.priority || 0) - (a.priority || 0))
        .slice(0, 5)
        .map((league: any) => ({
          id: league.id.toString(),
          name: league.name,
          logo: getLeagueEmoji(league.name),
          color: getLeagueColor(league.name)
        }));
    }

    // Fallback to static data
    return [
      { id: 'premier-league', name: 'Premier League', logo: '🏴󠁧󠁢󠁥󠁮󠁧󠁿', color: 'from-purple-500 to-pink-500' },
      { id: 'la-liga', name: 'La Liga', logo: '🇪🇸', color: 'from-red-500 to-orange-500' },
      { id: 'bundesliga', name: 'Bundesliga', logo: '🇩🇪', color: 'from-red-600 to-black' },
      { id: 'serie-a', name: 'Serie A', logo: '🇮🇹', color: 'from-green-500 to-red-500' },
      { id: 'champions-league', name: 'Champions League', logo: '🏆', color: 'from-blue-600 to-purple-600' }
    ];
  }, []);

  // Helper functions for league styling
  const getLeagueEmoji = (leagueName: string): string => {
    const name = leagueName.toLowerCase();
    if (name.includes('premier league')) return '🏴󠁧󠁢󠁥󠁮󠁧󠁿';
    if (name.includes('la liga')) return '🇪🇸';
    if (name.includes('bundesliga')) return '🇩🇪';
    if (name.includes('serie a')) return '🇮🇹';
    if (name.includes('ligue 1')) return '🇫🇷';
    if (name.includes('champions league')) return '🏆';
    if (name.includes('europa league')) return '🥈';
    return '⚽';
  };

  const getLeagueColor = (leagueName: string): string => {
    const name = leagueName.toLowerCase();
    if (name.includes('premier league')) return 'from-purple-500 to-pink-500';
    if (name.includes('la liga')) return 'from-red-500 to-orange-500';
    if (name.includes('bundesliga')) return 'from-red-600 to-black';
    if (name.includes('serie a')) return 'from-green-500 to-red-500';
    if (name.includes('ligue 1')) return 'from-blue-500 to-blue-700';
    if (name.includes('champions league')) return 'from-blue-600 to-purple-600';
    if (name.includes('europa league')) return 'from-orange-500 to-yellow-500';
    return 'from-gray-500 to-gray-700';
  };

  return (
    <div className="sticky top-20 z-30 bg-white/80 backdrop-blur-xl border-b border-white/20">
      <div className="container mx-auto px-4 py-4">
        {/* Main Filter Bar */}
        <div className="flex flex-col lg:flex-row gap-4 items-stretch lg:items-center">
          {/* Smart Search with AI Suggestions */}
          <div className="flex-1 relative" ref={searchRef}>
            <div className={`
              relative overflow-hidden rounded-2xl transition-all duration-300 ease-out
              ${isSearchFocused
                ? 'ring-2 ring-blue-500/50 shadow-2xl scale-105'
                : 'shadow-lg hover:shadow-xl'
              }
              bg-white/90 backdrop-blur-sm border border-white/30
            `}>
              <div className="flex items-center">
                <div className="pl-4 pr-2">
                  <span className="text-xl">🔍</span>
                </div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => {
                    onSearchChange(e.target.value);
                    setShowSuggestions(true);
                  }}
                  onFocus={() => {
                    setIsSearchFocused(true);
                    setShowSuggestions(true);
                  }}
                  placeholder="Search teams, leagues, or matches..."
                  className="flex-1 py-4 bg-transparent outline-none text-gray-900 placeholder-gray-500"
                />
                {searchQuery && (
                  <button
                    onClick={() => {
                      onSearchChange('');
                      setShowSuggestions(false);
                    }}
                    className="p-2 mr-2 rounded-full hover:bg-gray-100 transition-colors"
                  >
                    <span className="text-gray-400">✕</span>
                  </button>
                )}
              </div>

              {/* AI Suggestions Dropdown */}
              {showSuggestions && (searchQuery || suggestions.length > 0) && (
                <div className="absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/30 overflow-hidden animate-slide-down">
                  {/* Quick Suggestions */}
                  {suggestions.length > 0 && (
                    <div className="p-4 border-b border-gray-100">
                      <div className="text-xs font-medium text-gray-500 mb-2">AI SUGGESTIONS</div>
                      <div className="space-y-1">
                        {suggestions.slice(0, 5).map((suggestion, index) => (
                          <button
                            key={index}
                            onClick={() => {
                              onSearchChange(suggestion);
                              setShowSuggestions(false);
                            }}
                            className="w-full text-left px-3 py-2 rounded-lg hover:bg-blue-50 transition-colors text-sm"
                          >
                            <span className="text-blue-600">🤖</span> {suggestion}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Recommendations */}
                  {recommendations.length > 0 && (
                    <div className="p-4">
                      <div className="text-xs font-medium text-gray-500 mb-2">RECOMMENDED FOR YOU</div>
                      <div className="space-y-2">
                        {recommendations.slice(0, 3).map((rec, index) => (
                          <div
                            key={index}
                            className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                          >
                            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white text-sm">
                              {rec.icon}
                            </div>
                            <div className="flex-1">
                              <div className="font-medium text-sm">{rec.title}</div>
                              <div className="text-xs text-gray-500">{rec.description}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Quick Filter Pills */}
          <div className="flex flex-wrap gap-2">
            {filterOptions.map((option) => (
              <button
                key={option.key}
                onClick={() => {
                  const newFilters = { ...filters };
                  if (activeFilter === option.key) {
                    delete newFilters[option.key];
                    setActiveFilter(null);
                  } else {
                    newFilters[option.key] = true;
                    setActiveFilter(option.key);
                  }
                  onFiltersChange(newFilters);
                }}
                className={`
                  flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-300 ease-out
                  transform hover:scale-105 active:scale-95
                  ${activeFilter === option.key
                    ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
                    : 'bg-white/90 text-gray-700 hover:bg-white border border-white/30'
                  }
                `}
              >
                <span>{option.icon}</span>
                <span className="font-medium text-sm">{option.label}</span>
                <span className={`
                  text-xs px-2 py-0.5 rounded-full
                  ${activeFilter === option.key
                    ? 'bg-white/20 text-white'
                    : 'bg-gray-100 text-gray-600'
                  }
                `}>
                  {option.count}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Quick League Selector */}
        <div className="mt-4 flex flex-wrap gap-3">
          <div className="text-sm font-medium text-gray-600 flex items-center">
            <span className="mr-2">🏆</span>
            Quick Access:
          </div>
          {quickLeagues.map((league) => (
            <button
              key={league.id}
              onClick={() => onLeagueChange(selectedLeague === league.id ? null : league.id)}
              className={`
                flex items-center space-x-2 px-3 py-1.5 rounded-full transition-all duration-300 ease-out
                transform hover:scale-105 active:scale-95
                ${selectedLeague === league.id
                  ? `bg-gradient-to-r ${league.color} text-white shadow-lg`
                  : 'bg-white/70 text-gray-700 hover:bg-white border border-white/30'
                }
              `}
            >
              <span>{league.logo}</span>
              <span className="text-xs font-medium">{league.name}</span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};
