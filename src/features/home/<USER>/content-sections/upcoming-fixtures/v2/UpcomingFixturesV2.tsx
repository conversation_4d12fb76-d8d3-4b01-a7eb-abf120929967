'use client';

import React, { useState, useEffect } from 'react';
import { UpcomingFixturesProps } from '../types';
import { FluidHeader } from './components/FluidHeader';
import { SmartFilterBar } from './components/SmartFilterBar';
import { ImmersiveFixtureGrid } from './components/ImmersiveFixtureGrid';
import { FloatingActionPanel } from './components/FloatingActionPanel';
import { useAdvancedFixtures } from './hooks/useAdvancedFixtures';
import { useThemeAdaptation } from './hooks/useThemeAdaptation';
import { useGestureControls } from './hooks/useGestureControls';

const UpcomingFixturesV2: React.FC<UpcomingFixturesProps> = ({
  className = ''
}) => {
  const [viewMode, setViewMode] = useState<'grid' | 'timeline' | 'immersive'>('grid');
  const [selectedLeague, setSelectedLeague] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Advanced hooks for v2 features
  const {
    fixtures,
    leagues,
    isLoading,
    error,
    filters,
    updateFilters,
    smartSuggestions,
    aiRecommendations,
    refetch
  } = useAdvancedFixtures();

  const { currentTheme, adaptTheme } = useThemeAdaptation(selectedLeague);
  const { gestureHandlers } = useGestureControls({
    onSwipeLeft: () => setViewMode('timeline'),
    onSwipeRight: () => setViewMode('grid'),
    onPinch: () => setIsFullscreen(!isFullscreen)
  });

  // Dynamic background based on selected league
  useEffect(() => {
    if (selectedLeague) {
      adaptTheme(selectedLeague);
    }
  }, [selectedLeague, adaptTheme]);

  // Make leagues available globally for SmartFilterBar
  useEffect(() => {
    (window as any).v2Leagues = leagues;
  }, [leagues]);

  const containerClasses = `
    min-h-screen relative overflow-hidden transition-all duration-1000 ease-out
    ${currentTheme.background}
    ${isFullscreen ? 'fixed inset-0 z-50' : ''}
    ${className}
  `;

  return (
    <div className={containerClasses} {...gestureHandlers}>
      {/* Dynamic Background Effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Animated Gradient Orbs */}
        <div className={`absolute -top-40 -right-40 w-80 h-80 rounded-full blur-3xl opacity-20 animate-pulse-slow ${currentTheme.primaryOrb}`}></div>
        <div className={`absolute -bottom-40 -left-40 w-96 h-96 rounded-full blur-3xl opacity-15 animate-float ${currentTheme.secondaryOrb}`}></div>

        {/* Floating Particles */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className={`absolute w-2 h-2 rounded-full opacity-30 animate-float-random ${currentTheme.particles}`}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`,
                animationDuration: `${3 + Math.random() * 4}s`
              }}
            />
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10">
        {/* Fluid Header with Morphing Elements */}
        <FluidHeader
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          isFullscreen={isFullscreen}
          onToggleFullscreen={() => setIsFullscreen(!isFullscreen)}
          theme={currentTheme}
        />

        {/* Smart Filter Bar with AI Suggestions */}
        <SmartFilterBar
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          selectedLeague={selectedLeague}
          onLeagueChange={setSelectedLeague}
          filters={filters}
          onFiltersChange={updateFilters}
          suggestions={smartSuggestions}
          recommendations={aiRecommendations}
          theme={currentTheme}
        />

        {/* Immersive Fixture Grid with 3D Effects */}
        <ImmersiveFixtureGrid
          fixtures={fixtures}
          leagues={leagues}
          viewMode={viewMode}
          selectedLeague={selectedLeague}
          searchQuery={searchQuery}
          isLoading={isLoading}
          error={error}
          theme={currentTheme}
          onFixtureSelect={(fixture) => {
            // Handle fixture selection with smooth animation
            console.log('Selected fixture:', fixture);
          }}
          onRetry={refetch}
        />

        {/* Floating Action Panel */}
        <FloatingActionPanel
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          onRefresh={() => window.location.reload()}
          onShare={() => navigator.share?.({ title: 'Upcoming Fixtures' })}
          onFavorite={() => console.log('Add to favorites')}
          theme={currentTheme}
        />
      </div>

      {/* Loading Overlay with Morphing Animation */}
      {isLoading && (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="relative">
            {/* Morphing Loader */}
            <div className="w-16 h-16 relative">
              <div className={`absolute inset-0 rounded-full border-4 border-transparent ${currentTheme.loaderBorder} animate-spin`}></div>
              <div className={`absolute inset-2 rounded-full ${currentTheme.loaderCore} animate-pulse`}></div>
            </div>
            <div className={`mt-4 text-center font-medium ${currentTheme.loaderText}`}>
              Loading amazing fixtures...
            </div>
          </div>
        </div>
      )}

      {/* Error State with Elegant Animation */}
      {error && (
        <div className="fixed bottom-4 right-4 z-50 animate-slide-up">
          <div className="bg-red-500/90 backdrop-blur-sm text-white px-6 py-4 rounded-xl shadow-2xl">
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
                <span className="text-sm">⚠️</span>
              </div>
              <div>
                <div className="font-medium">Something went wrong</div>
                <div className="text-sm opacity-90">{error}</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UpcomingFixturesV2;
