import { useState, useEffect, useCallback, useMemo } from 'react';

interface AdvancedFilters {
  dateRange?: { start: string; end: string };
  leagues?: string[];
  status?: string[];
  isHot?: boolean;
  hasOdds?: boolean;
  minImportance?: number;
}

interface SmartSuggestion {
  query: string;
  type: 'team' | 'league' | 'competition';
  confidence: number;
}

interface AIRecommendation {
  id: string;
  title: string;
  description: string;
  icon: string;
  action: () => void;
}

export const useAdvancedFixtures = () => {
  const [fixtures, setFixtures] = useState<any[]>([]);
  const [leagues, setLeagues] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<AdvancedFilters>({});
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [userPreferences, setUserPreferences] = useState<any>({});

  // Fetch fixtures with advanced filtering
  const fetchFixtures = useCallback(async (advancedFilters?: AdvancedFilters) => {
    setIsLoading(true);
    setError(null);

    try {
      // Build query parameters
      const params = new URLSearchParams();
      
      if (advancedFilters?.dateRange) {
        params.append('startDate', advancedFilters.dateRange.start);
        params.append('endDate', advancedFilters.dateRange.end);
      }
      
      if (advancedFilters?.leagues?.length) {
        params.append('leagues', advancedFilters.leagues.join(','));
      }
      
      if (advancedFilters?.status?.length) {
        params.append('status', advancedFilters.status.join(','));
      }

      // Mock API call - replace with actual endpoint
      const response = await fetch(`/api/football/fixtures/upcoming-and-live?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch fixtures');
      }

      const data = await response.json();
      
      // Process and enhance fixtures data
      const enhancedFixtures = data.fixtures?.map((fixture: any) => ({
        ...fixture,
        // Add AI-powered enhancements
        isHot: Math.random() > 0.8, // Mock hot detection
        importance: Math.floor(Math.random() * 10) + 1,
        predictedScore: generatePredictedScore(),
        aiInsights: generateAIInsights(fixture),
        socialBuzz: Math.floor(Math.random() * 1000),
        viewerInterest: Math.random()
      })) || [];

      setFixtures(enhancedFixtures);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch leagues
  const fetchLeagues = useCallback(async () => {
    try {
      const response = await fetch('/api/football/leagues?active=true');
      if (response.ok) {
        const data = await response.json();
        setLeagues(data.leagues || []);
      }
    } catch (err) {
      console.error('Failed to fetch leagues:', err);
    }
  }, []);

  // Generate smart suggestions based on search history and user behavior
  const smartSuggestions = useMemo((): string[] => {
    const suggestions = [
      'Manchester United vs Liverpool',
      'Champions League matches',
      'Premier League today',
      'Barcelona fixtures',
      'Live matches now',
      'Hot matches this week',
      'El Clasico',
      'Derby matches'
    ];

    // Add personalized suggestions based on search history
    const personalizedSuggestions = searchHistory
      .slice(-5)
      .map(search => `${search} upcoming matches`);

    return [...suggestions, ...personalizedSuggestions].slice(0, 8);
  }, [searchHistory]);

  // Generate AI recommendations
  const aiRecommendations = useMemo((): AIRecommendation[] => {
    return [
      {
        id: 'trending-matches',
        title: 'Trending Matches',
        description: 'Most talked about fixtures this week',
        icon: '📈',
        action: () => updateFilters({ ...filters, minImportance: 7 })
      },
      {
        id: 'your-teams',
        title: 'Your Favorite Teams',
        description: 'Matches featuring your followed teams',
        icon: '⭐',
        action: () => {
          // Load user's favorite teams
          const favoriteLeagues = userPreferences.favoriteLeagues || [];
          updateFilters({ ...filters, leagues: favoriteLeagues });
        }
      },
      {
        id: 'live-now',
        title: 'Live Right Now',
        description: 'Matches currently being played',
        icon: '🔴',
        action: () => updateFilters({ ...filters, status: ['LIVE', '1H', '2H', 'HT'] })
      },
      {
        id: 'weekend-highlights',
        title: 'Weekend Highlights',
        description: 'Best matches coming this weekend',
        icon: '🏆',
        action: () => {
          const weekend = getWeekendDateRange();
          updateFilters({ ...filters, dateRange: weekend, minImportance: 6 });
        }
      }
    ];
  }, [filters, userPreferences]);

  // Update filters with smart merging
  const updateFilters = useCallback((newFilters: AdvancedFilters) => {
    setFilters(prev => {
      const merged = { ...prev, ...newFilters };
      fetchFixtures(merged);
      return merged;
    });
  }, [fetchFixtures]);

  // Add to search history
  const addToSearchHistory = useCallback((query: string) => {
    if (query.trim() && !searchHistory.includes(query)) {
      setSearchHistory(prev => [query, ...prev.slice(0, 9)]);
    }
  }, [searchHistory]);

  // Initialize data
  useEffect(() => {
    fetchFixtures();
    fetchLeagues();
    
    // Load user preferences from localStorage
    const savedPreferences = localStorage.getItem('userPreferences');
    if (savedPreferences) {
      setUserPreferences(JSON.parse(savedPreferences));
    }

    // Load search history
    const savedHistory = localStorage.getItem('searchHistory');
    if (savedHistory) {
      setSearchHistory(JSON.parse(savedHistory));
    }
  }, [fetchFixtures, fetchLeagues]);

  // Save preferences and history
  useEffect(() => {
    localStorage.setItem('userPreferences', JSON.stringify(userPreferences));
  }, [userPreferences]);

  useEffect(() => {
    localStorage.setItem('searchHistory', JSON.stringify(searchHistory));
  }, [searchHistory]);

  return {
    fixtures,
    leagues,
    isLoading,
    error,
    filters,
    updateFilters,
    smartSuggestions,
    aiRecommendations,
    addToSearchHistory,
    refetch: () => fetchFixtures(filters)
  };
};

// Helper functions
function generatePredictedScore() {
  return {
    home: Math.floor(Math.random() * 4),
    away: Math.floor(Math.random() * 4),
    confidence: Math.random()
  };
}

function generateAIInsights(fixture: any) {
  const insights = [
    'High-scoring match expected',
    'Defensive battle likely',
    'Home advantage significant',
    'Key players missing',
    'Historical rivalry',
    'Title implications',
    'Relegation battle',
    'European qualification at stake'
  ];
  
  return insights[Math.floor(Math.random() * insights.length)];
}

function getWeekendDateRange() {
  const now = new Date();
  const dayOfWeek = now.getDay();
  const daysUntilSaturday = (6 - dayOfWeek) % 7;
  
  const saturday = new Date(now);
  saturday.setDate(now.getDate() + daysUntilSaturday);
  
  const sunday = new Date(saturday);
  sunday.setDate(saturday.getDate() + 1);
  
  return {
    start: saturday.toISOString().split('T')[0],
    end: sunday.toISOString().split('T')[0]
  };
}
