import { useCallback, useRef, useState, useEffect } from 'react';

interface GestureConfig {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  onPinch?: (scale: number) => void;
  onDoubleTap?: () => void;
  onLongPress?: () => void;
  swipeThreshold?: number;
  pinchThreshold?: number;
  longPressDelay?: number;
}

interface TouchPoint {
  x: number;
  y: number;
  timestamp: number;
}

export const useGestureControls = (config: GestureConfig = {}) => {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    onPinch,
    onDoubleTap,
    onLongPress,
    swipeThreshold = 50,
    pinchThreshold = 0.1,
    longPressDelay = 500
  } = config;

  const [isGestureActive, setIsGestureActive] = useState(false);
  const touchStartRef = useRef<TouchPoint | null>(null);
  const touchEndRef = useRef<TouchPoint | null>(null);
  const lastTapRef = useRef<number>(0);
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const initialPinchDistanceRef = useRef<number>(0);
  const currentPinchDistanceRef = useRef<number>(0);

  // Calculate distance between two touch points
  const getDistance = useCallback((touch1: Touch, touch2: Touch) => {
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.sqrt(dx * dx + dy * dy);
  }, []);

  // Handle touch start
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0];
    const now = Date.now();

    setIsGestureActive(true);
    
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: now
    };

    // Handle multi-touch (pinch)
    if (e.touches.length === 2) {
      initialPinchDistanceRef.current = getDistance(e.touches[0], e.touches[1]);
      currentPinchDistanceRef.current = initialPinchDistanceRef.current;
    }

    // Handle double tap
    if (onDoubleTap) {
      const timeSinceLastTap = now - lastTapRef.current;
      if (timeSinceLastTap < 300) {
        onDoubleTap();
        lastTapRef.current = 0; // Reset to prevent triple tap
      } else {
        lastTapRef.current = now;
      }
    }

    // Handle long press
    if (onLongPress) {
      longPressTimerRef.current = setTimeout(() => {
        onLongPress();
      }, longPressDelay);
    }
  }, [getDistance, onDoubleTap, onLongPress, longPressDelay]);

  // Handle touch move
  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    // Clear long press timer on move
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }

    // Handle pinch gesture
    if (e.touches.length === 2 && onPinch) {
      const newDistance = getDistance(e.touches[0], e.touches[1]);
      const scale = newDistance / initialPinchDistanceRef.current;
      
      // Only trigger if significant change
      if (Math.abs(scale - 1) > pinchThreshold) {
        onPinch(scale);
      }
      
      currentPinchDistanceRef.current = newDistance;
    }

    // Update touch end position for swipe detection
    const touch = e.touches[0];
    touchEndRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now()
    };
  }, [getDistance, onPinch, pinchThreshold]);

  // Handle touch end
  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    setIsGestureActive(false);

    // Clear long press timer
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }

    // Handle swipe gestures
    if (touchStartRef.current && touchEndRef.current) {
      const deltaX = touchEndRef.current.x - touchStartRef.current.x;
      const deltaY = touchEndRef.current.y - touchStartRef.current.y;
      const deltaTime = touchEndRef.current.timestamp - touchStartRef.current.timestamp;
      
      // Only consider fast gestures (< 300ms) as swipes
      if (deltaTime < 300) {
        const absX = Math.abs(deltaX);
        const absY = Math.abs(deltaY);

        // Horizontal swipes
        if (absX > swipeThreshold && absX > absY) {
          if (deltaX > 0 && onSwipeRight) {
            onSwipeRight();
          } else if (deltaX < 0 && onSwipeLeft) {
            onSwipeLeft();
          }
        }
        // Vertical swipes
        else if (absY > swipeThreshold && absY > absX) {
          if (deltaY > 0 && onSwipeDown) {
            onSwipeDown();
          } else if (deltaY < 0 && onSwipeUp) {
            onSwipeUp();
          }
        }
      }
    }

    // Reset touch references
    touchStartRef.current = null;
    touchEndRef.current = null;
    initialPinchDistanceRef.current = 0;
    currentPinchDistanceRef.current = 0;
  }, [onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, swipeThreshold]);

  // Handle mouse events for desktop
  const [mouseDown, setMouseDown] = useState(false);
  const mouseStartRef = useRef<{ x: number; y: number } | null>(null);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setMouseDown(true);
    mouseStartRef.current = { x: e.clientX, y: e.clientY };
    
    // Handle double click
    if (onDoubleTap && e.detail === 2) {
      onDoubleTap();
    }
  }, [onDoubleTap]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!mouseDown || !mouseStartRef.current) return;

    const deltaX = e.clientX - mouseStartRef.current.x;
    const deltaY = e.clientY - mouseStartRef.current.y;
    
    // Visual feedback for drag
    setIsGestureActive(Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10);
  }, [mouseDown]);

  const handleMouseUp = useCallback((e: React.MouseEvent) => {
    if (!mouseDown || !mouseStartRef.current) return;

    const deltaX = e.clientX - mouseStartRef.current.x;
    const deltaY = e.clientY - mouseStartRef.current.y;
    const absX = Math.abs(deltaX);
    const absY = Math.abs(deltaY);

    // Treat mouse drags as swipes
    if (absX > swipeThreshold && absX > absY) {
      if (deltaX > 0 && onSwipeRight) {
        onSwipeRight();
      } else if (deltaX < 0 && onSwipeLeft) {
        onSwipeLeft();
      }
    } else if (absY > swipeThreshold && absY > absX) {
      if (deltaY > 0 && onSwipeDown) {
        onSwipeDown();
      } else if (deltaY < 0 && onSwipeUp) {
        onSwipeUp();
      }
    }

    setMouseDown(false);
    setIsGestureActive(false);
    mouseStartRef.current = null;
  }, [mouseDown, onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, swipeThreshold]);

  // Keyboard shortcuts
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowLeft':
        e.preventDefault();
        onSwipeLeft?.();
        break;
      case 'ArrowRight':
        e.preventDefault();
        onSwipeRight?.();
        break;
      case 'ArrowUp':
        e.preventDefault();
        onSwipeUp?.();
        break;
      case 'ArrowDown':
        e.preventDefault();
        onSwipeDown?.();
        break;
      case 'Enter':
      case ' ':
        e.preventDefault();
        onDoubleTap?.();
        break;
    }
  }, [onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, onDoubleTap]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (longPressTimerRef.current) {
        clearTimeout(longPressTimerRef.current);
      }
    };
  }, []);

  // Return gesture handlers
  const gestureHandlers = {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
    onMouseDown: handleMouseDown,
    onMouseMove: handleMouseMove,
    onMouseUp: handleMouseUp,
    onKeyDown: handleKeyDown,
    tabIndex: 0, // Make element focusable for keyboard events
    style: {
      touchAction: 'none', // Prevent default touch behaviors
      userSelect: 'none' as const, // Prevent text selection during gestures
      cursor: isGestureActive ? 'grabbing' : 'grab'
    }
  };

  return {
    gestureHandlers,
    isGestureActive
  };
};
