import React from 'react';
import { ProcessedFixture } from '../../types';
import { LiveStatusIndicator, MatchTimeDisplay } from './LiveStatusIndicator';

interface FixtureCardProps {
  fixture: ProcessedFixture;
  currentTime: Date;
  variant?: 'compact' | 'full';
}

export const FixtureCard: React.FC<FixtureCardProps> = ({
  fixture,
  currentTime,
  variant = 'compact'
}) => {
  const getTimeUntilKickoff = () => {
    if (!fixture.date) return 'Unknown';

    if (['1H', '2H', 'HT', 'LIVE'].includes(fixture.status)) {
      return `${fixture.minute || 'LIVE'}`;
    }
    if (['FT', 'AET', 'PEN', 'FINISHED'].includes(fixture.status)) {
      return 'Finished';
    }

    const now = currentTime;
    const matchDate = new Date(fixture.date);
    const diff = matchDate.getTime() - now.getTime();

    if (diff < 0) return 'Started';

    const hoursLeft = Math.floor(diff / (1000 * 60 * 60));
    const minutesLeft = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const daysLeft = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (daysLeft > 0) return `${daysLeft}d ${hoursLeft % 24}h`;
    if (hoursLeft > 0) return `${hoursLeft}h ${minutesLeft}m`;
    return `${minutesLeft}m`;
  };

  const getStatusDisplay = () => {
    if (['1H', '2H', 'HT', 'LIVE'].includes(fixture.status)) {
      return { text: 'LIVE', color: 'text-red-600', bg: 'bg-red-100', border: 'border-red-200' };
    }
    if (['FT', 'AET', 'PEN', 'FINISHED'].includes(fixture.status)) {
      return { text: 'FINISHED', color: 'text-gray-600', bg: 'bg-gray-100', border: 'border-gray-200' };
    }
    return { text: 'UPCOMING', color: 'text-blue-600', bg: 'bg-blue-100', border: 'border-blue-200' };
  };

  const getImportanceIndicator = () => {
    if (fixture.isVip) return { icon: '👑', color: 'text-purple-600', bg: 'bg-purple-100' };
    if (fixture.isHot) return { icon: '🔥', color: 'text-red-600', bg: 'bg-red-100' };
    if (fixture.isTrending) return { icon: '⭐', color: 'text-yellow-600', bg: 'bg-yellow-100' };
    return null;
  };

  const statusDisplay = getStatusDisplay();
  const importance = getImportanceIndicator();

  const handleClick = () => {
    window.open(`/${fixture.slug}/${fixture.externalId}`, '_blank');
  };

  return (
    <div
      onClick={handleClick}
      className="bg-white rounded-xl border border-gray-200 p-8 hover:shadow-lg transition-all duration-200 cursor-pointer group hover:border-blue-300"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span className="text-sm font-medium text-gray-600">
            {fixture.competition}
          </span>
          {fixture.round && (
            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
              {fixture.round}
            </span>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${statusDisplay.bg} ${statusDisplay.color} ${statusDisplay.border} border`}>
            {statusDisplay.text}
          </span>
          {importance && (
            <span className={`w-8 h-8 rounded-full ${importance.bg} flex items-center justify-center text-sm`}>
              {importance.icon}
            </span>
          )}
        </div>
      </div>

      {/* Teams */}
      <div className="flex items-center justify-between">
        {/* Home Team */}
        <div className="flex items-center space-x-4 flex-1">
          <div className="relative w-16 h-16 flex-shrink-0">
            {fixture.homeLogo ? (
              <img
                src={`${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/${fixture.homeLogo}`}
                alt={fixture.homeTeam}
                className="w-full h-full object-contain rounded-xl shadow-md hover:shadow-lg transition-shadow duration-200"
                onError={(e) => {
                  // Fallback to team initial
                  const target = e.currentTarget;
                  target.style.display = 'none';
                  const fallback = target.nextElementSibling as HTMLElement;
                  if (fallback) fallback.style.display = 'flex';
                }}
              />
            ) : null}
            <div
              className="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-md"
              style={{ display: fixture.homeLogo ? 'none' : 'flex' }}
            >
              {fixture.homeTeam.charAt(0)}
            </div>
          </div>
          <div>
            <div className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
              {fixture.homeTeam}
            </div>
            <div className="text-sm text-gray-500 flex items-center space-x-1">
              <span>{fixture.homeFlag}</span>
              <span>Home</span>
            </div>
          </div>
        </div>

        {/* Score/VS */}
        <div className="text-center px-4">
          {fixture.homeScore !== undefined && fixture.awayScore !== undefined ? (
            <div className="text-xl font-bold text-gray-900">
              {fixture.homeScore} - {fixture.awayScore}
            </div>
          ) : (
            <div className="text-lg font-semibold text-gray-400">VS</div>
          )}
          <div className="text-sm text-gray-500 mt-1">
            {getTimeUntilKickoff()}
          </div>
        </div>

        {/* Away Team */}
        <div className="flex items-center space-x-4 flex-1 justify-end">
          <div className="text-right">
            <div className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
              {fixture.awayTeam}
            </div>
            <div className="text-sm text-gray-500 flex items-center justify-end space-x-1">
              <span>Away</span>
              <span>{fixture.awayFlag}</span>
            </div>
          </div>
          <div className="relative w-16 h-16 flex-shrink-0">
            {fixture.awayLogo ? (
              <img
                src={`${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65'}/${fixture.awayLogo}`}
                alt={fixture.awayTeam}
                className="w-full h-full object-contain rounded-xl shadow-md hover:shadow-lg transition-shadow duration-200"
                onError={(e) => {
                  // Fallback to team initial
                  const target = e.currentTarget;
                  target.style.display = 'none';
                  const fallback = target.nextElementSibling as HTMLElement;
                  if (fallback) fallback.style.display = 'flex';
                }}
              />
            ) : null}
            <div
              className="absolute inset-0 bg-gradient-to-br from-red-500 to-orange-600 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-md"
              style={{ display: fixture.awayLogo ? 'none' : 'flex' }}
            >
              {fixture.awayTeam.charAt(0)}
            </div>
          </div>
        </div>
      </div>

      {/* Venue */}
      {fixture.venue && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <span>🏟️</span>
            <span>{fixture.venue}</span>
          </div>
        </div>
      )}
    </div>
  );
};
