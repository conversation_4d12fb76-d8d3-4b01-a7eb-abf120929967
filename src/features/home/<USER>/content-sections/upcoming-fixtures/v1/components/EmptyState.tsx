import React from 'react';

interface EmptyStateProps {
  className?: string;
  title?: string;
  description?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  className = '',
  title = 'No Upcoming Fixtures',
  description = 'There are no scheduled matches at the moment. Check back later for updates.'
}) => {
  return (
    <section className={`py-16 bg-gradient-to-br from-gray-50 to-white ${className}`}>
      <div className="container mx-auto px-4">
        <div className="text-center max-w-md mx-auto">
          {/* Empty icon */}
          <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-4xl">⚽</span>
          </div>

          {/* Empty message */}
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            No Upcoming Fixtures
          </h3>
          
          <p className="text-gray-600 mb-6">
            There are no scheduled matches at the moment. Check back later for updates.
          </p>

          {/* Suggestions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">
              What you can do:
            </h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Check back in a few hours</li>
              <li>• Browse completed matches</li>
              <li>• Explore league standings</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
};
