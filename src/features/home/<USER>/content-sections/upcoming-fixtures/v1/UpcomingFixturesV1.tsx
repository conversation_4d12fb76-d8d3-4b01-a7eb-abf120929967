'use client';

import React from 'react';
import { UpcomingFixturesProps } from '../types';
import { LeagueList } from './components/LeagueList';
import { DateBasedFixtures } from './components/DateBasedFixtures';

const UpcomingFixturesV1: React.FC<UpcomingFixturesProps> = ({
  className = ''
}) => {
  // Enhanced layout with optimized components

  return (
    <section className={`py-16 bg-gradient-to-br from-gray-50 to-white ${className}`}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <span className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
            <span>Leagues & Fixtures</span>
          </div>

          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Football Hub
          </h2>

          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Explore leagues and discover today&apos;s exciting matches
          </p>
        </div>

        {/* Clean Two Column Layout */}
        <div className="grid grid-cols-1 xl:grid-cols-4 lg:grid-cols-1 gap-8 max-w-7xl mx-auto">
          {/* League Sidebar - 25% width */}
          <div className="xl:col-span-1 lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <LeagueList />
            </div>
          </div>

          {/* Main Content - 75% width */}
          <div className="xl:col-span-3 lg:col-span-1">
            <DateBasedFixtures />
          </div>
        </div>

        {/* Footer Info */}
        <div className="text-center mt-12">
          <div className="text-sm text-gray-600">
            Click on any league to explore detailed information and standings
          </div>
        </div>
      </div>
    </section>
  );
};

export default UpcomingFixturesV1;
