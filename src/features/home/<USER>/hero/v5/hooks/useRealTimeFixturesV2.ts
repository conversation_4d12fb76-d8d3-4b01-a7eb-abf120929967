'use client';

import { useState, useEffect } from 'react';

// API Response Types based on real data structure
interface ApiVenue {
  id: number;
  name: string;
  city: string;
}

interface ApiPeriods {
  first: number;
  second: number;
}

interface ApiFixture {
  id: number;
  externalId: number;
  leagueId: number;
  leagueName: string;
  isHot: boolean;
  season: number;
  round: string;
  homeTeamId: number;
  homeTeamName: string;
  homeTeamLogo: string;
  awayTeamId: number;
  awayTeamName: string;
  awayTeamLogo: string;
  slug: string;
  date: string;
  venue: ApiVenue;
  referee: string;
  status: string;
  statusLong: string;
  statusExtra: number;
  elapsed: number;
  goalsHome: number;
  goalsAway: number;
  scoreHalftimeHome: number;
  scoreHalftimeAway: number;
  scoreFulltimeHome: number;
  scoreFulltimeAway: number;
  periods: ApiPeriods;
  timestamp: string;
  thumb: string | null;
  computedStatus: string;
  minutesUntilStart: number;
}

interface ApiResponse {
  data: ApiFixture[];
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    limit: number;
  };
  status: number;
}

// Processed Fixture Type for UI
export interface ProcessedFixture {
  id: number;
  homeTeam: {
    id: number;
    name: string;
    logo: string;
  };
  awayTeam: {
    id: number;
    name: string;
    logo: string;
  };
  league: {
    id: number;
    name: string;
  };
  match: {
    date: string;
    time: string;
    venue: string;
    status: string;
    statusLong: string;
    elapsed: number;
  };
  score: {
    home: number;
    away: number;
    halftimeHome: number;
    halftimeAway: number;
  };
  isLive: boolean;
  isUpcoming: boolean;
  minutesUntilStart: number;
}

export const useRealTimeFixturesV2 = () => {
  console.log('🎣 useRealTimeFixturesV2 hook initialized');

  const [fixtures, setFixtures] = useState<ProcessedFixture[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Helper function to build CDN image URL
  const buildCDNImageUrl = (path: string): string => {
    const cdnDomain = process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65';
    if (!path) return `${cdnDomain}/public/images/teams/default.png`;

    // Remove leading slash if present
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    const fullUrl = `${cdnDomain}/${cleanPath}`;

    console.log('🖼️ Built CDN URL:', fullUrl);
    return fullUrl;
  };

  // Process API fixture to UI format
  const processFixture = (apiFixture: ApiFixture): ProcessedFixture => {
    const matchDate = new Date(apiFixture.date);
    const isLive = apiFixture.computedStatus === 'LIVE' || apiFixture.status === 'LIVE';
    const isUpcoming = apiFixture.computedStatus === 'UPCOMING' || apiFixture.status === 'NS';

    return {
      id: apiFixture.id,
      homeTeam: {
        id: apiFixture.homeTeamId,
        name: apiFixture.homeTeamName,
        logo: buildCDNImageUrl(apiFixture.homeTeamLogo)
      },
      awayTeam: {
        id: apiFixture.awayTeamId,
        name: apiFixture.awayTeamName,
        logo: buildCDNImageUrl(apiFixture.awayTeamLogo)
      },
      league: {
        id: apiFixture.leagueId,
        name: apiFixture.leagueName
      },
      match: {
        date: matchDate.toLocaleDateString(),
        time: matchDate.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        }),
        venue: `${apiFixture.venue.name}, ${apiFixture.venue.city}`,
        status: apiFixture.status,
        statusLong: apiFixture.statusLong,
        elapsed: apiFixture.elapsed
      },
      score: {
        home: apiFixture.goalsHome,
        away: apiFixture.goalsAway,
        halftimeHome: apiFixture.scoreHalftimeHome,
        halftimeAway: apiFixture.scoreHalftimeAway
      },
      isLive,
      isUpcoming,
      minutesUntilStart: apiFixture.minutesUntilStart || 0
    };
  };

  // Fetch fixtures from API
  const fetchFixtures = async () => {
    try {
      console.log('🚀 Starting fetchFixtures V2...');
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/football/fixtures?page=1&limit=20');
      console.log('📡 Response status:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse = await response.json();
      console.log('📊 API Response:', {
        status: result.status,
        dataLength: result.data?.length,
        totalItems: result.meta?.totalItems
      });

      if (result.data && Array.isArray(result.data)) {
        const processedFixtures = result.data.map(processFixture);
        setFixtures(processedFixtures);
        console.log('✅ Processed fixtures:', processedFixtures.length);
        console.log('🎯 Sample fixture:', processedFixtures[0]);
      } else {
        console.log('⚠️ No data array found in response');
        setFixtures([]);
      }
    } catch (err) {
      console.error('❌ Fetch error:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch fixtures');
      setFixtures([]);
    } finally {
      setIsLoading(false);
      console.log('🏁 Fetch completed');
    }
  };

  // Effect to fetch data on mount and set up polling
  useEffect(() => {
    console.log('🔄 useEffect triggered in V2');

    // Initial fetch
    fetchFixtures();

    // Set up polling every 30 seconds
    const interval = setInterval(() => {
      console.log('⏰ Polling interval triggered');
      fetchFixtures();
    }, 30000);

    return () => {
      console.log('🧹 Cleaning up interval');
      clearInterval(interval);
    };
  }, []);

  console.log('🎯 Hook returning:', {
    fixtures: fixtures.length,
    isLoading,
    error: error ? 'Has error' : 'No error'
  });

  return {
    fixtures,
    isLoading,
    error,
    refetch: fetchFixtures,
    totalMatches: fixtures.length
  };
};
